import React from "react";
import FeaturedCard from "../FeaturedCard/FeaturedCard";
import { featuredContents } from "@/data";
import { TFeaturedCard } from "@/typescript/types";

const FeaturedSection = () => {
  return (
    <section>
      <div className="container space-y-8">
        {featuredContents.map((featuredContent: TFeaturedCard, idx: number) => (
          <FeaturedCard key={idx} {...featuredContent} />
        ))}
      </div>
    </section>
  );
};

export default FeaturedSection;
