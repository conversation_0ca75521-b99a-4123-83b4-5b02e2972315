import React from "react";
import Link from "next/link";
import Image from "next/image";
import { DynamicIcon, dynamicIconImports } from "lucide-react/dynamic";
import { Button } from "@/components/ui/button";

interface WhySectionProps {
  title: string;
  description: string;
  image: string;
  points: {
    icon: keyof typeof dynamicIconImports;
    text: string;
  }[];
  buttonText: string;
  buttonLink: string;
}

const WhySection = ({
  title,
  description,
  image,
  points,
  buttonText,
  buttonLink,
}: WhySectionProps): React.JSX.Element => {
  return (
    <section>
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 items-center">
          <div>
            <Image src={image} alt={title} width={800} height={800} priority />
          </div>
          <div className="space-y-5">
            <h2 className="text-3xl xl:text-5xl font-bold text-secondary">{title}</h2>
            <p className="text-gray-600 text-base lg:text-lg xl:text-2xl leading-relaxed">
              {description}
            </p>
            <ul className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {points.map((point, idx) => (
                <li key={idx} className="flex items-center gap-2">
                  <span>
                    <DynamicIcon name={point.icon} size={20} className="text-secondary" />
                  </span>
                  <span className="text-secondary">{point.text}</span>
                </li>
              ))}
            </ul>
            <Button asChild className="px-8! py-3 w-auto h-auto rounded-full">
              <Link href={buttonLink}>
                {buttonText} <DynamicIcon name="arrow-right" size={20} />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhySection;
