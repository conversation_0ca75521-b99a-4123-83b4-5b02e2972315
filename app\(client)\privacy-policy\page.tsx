import React from "react";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import { privacyPolicyContents } from "@/data";
import { PrivacyPolicy, TermsOfUse } from "@/typescript/types";

const PrivacyPolicyPage = () => {
  return (
    <>
      <TopBanner title="Privacy Policy" />
      <section>
        <div className="container">
          <div className="space-y-2 text-gray-700">
            <p>
              This Privacy Policy describes how GAYROOM8.COM, LLC (“Gayroom8”) collects, uses and
              discloses your data through your access to, and use of, the Gayroom8 website
              (www.Gayroom8.com) including any subdomains thereof, and any other websites through
              which Gayroom8 makes its services available (individually and collectively,
              &quot;Site&quot;), the mobile, tablet and other smart device applications, and
              application program interfaces through which Gayroom8 makes its services available
              (individually and collectively, “Application”) and all associated services,
              (collectively, “Services”). The Site, Application and Services together are
              hereinafter collectively referred to as the “Platform”. In addition, this Privacy
              Policy provides you with information on your rights regarding your data, and the means
              to exercise these rights.
            </p>
            <p>The terms “you” and “your” refer to you as an individual.</p>
            <p>
              Gayroom8 is committed to being as transparent as possible about the use of your data,
              to allow you to understand the implications of the data processing implemented or the
              rights you have regarding your data.
            </p>
            <p>
              • All the information is made available to you, permanently and updated, in this
              Privacy Policy and in the Cookie Policy, which you can consult at your discretion;
            </p>
            <p>
              • Moreover, information regarding each processing of your data will also be provided
              to you as you interact with the Gayroom8 Platform.
            </p>
            <p>
              Any terms without definition provided in this Privacy Policy will have the same
              definition as the one given in the ‘Gayroom8 Terms & Conditions of Services’.{" "}
            </p>
            <p>
              <span className="font-semibold">IMPORTANT.</span> Gayroom8 implements and constantly
              updates administrative, technical and physical security measures in order to protect
              your data against unauthorized access, loss, destruction or alteration. However, the
              Internet is not a 100% secure environment and therefore Gayroom8 cannot fully
              guarantee the security of the transmission or the storage of your data.
            </p>
          </div>
          <hr className="my-5" />
          <div className="text-gray-700 space-y-5">
            {privacyPolicyContents.map((content: PrivacyPolicy, idx: number) => (
              <PrivacyPolicyContent key={idx} {...content} />
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

const PrivacyPolicyContent = ({ title, contents }: TermsOfUse) => {
  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold">{title}</h3>
      <ul className="space-y-3">
        {contents.map((content: string, idx: number) => (
          <li key={idx}>{content}</li>
        ))}
      </ul>
    </div>
  );
};

export default PrivacyPolicyPage;
