# Multi-Step Form System

A reusable, configurable multi-step form system built with React Hook Form, Zod validation, and shadcn/ui components.

## Features

- **Configurable Form Flows**: Different step sequences based on intent (rent/find) and space type (private-room/entire-place)
- **Type-Safe Validation**: Zod schemas with TypeScript integration
- **Reusable Components**: Modular step components that adapt to different form configurations
- **Progress Tracking**: Visual progress indicator and step navigation
- **Form State Management**: Centralized form state with validation per step
- **Responsive Design**: Mobile-friendly UI components

## Quick Start

```tsx
import { MultiStepForm } from "@/components/Forms/MultiStepForm";

function MyForm() {
  const handleSubmit = (data) => {
    console.log("Form submitted:", data);
  };

  return (
    <MultiStepForm
      intent="rent"
      spaceType="private-room"
      onSubmit={handleSubmit}
      title="List Your Private Room"
    />
  );
}
```

## Available Configurations

### Rent + Private Room
- **Steps**: Basic Info → About You → Home Details → Room Details → Roommate Preferences
- **Use Case**: Listing a private room for rent

### Rent + Entire Place
- **Steps**: Basic Info → About You → Space Details → Rental Basics → Tenant Preferences
- **Use Case**: Listing an entire property for rent

### Find + Private Room
- **Steps**: Basic Info → Extended About You → Room Preferences → Roommate Preferences
- **Use Case**: Looking for a private room to rent

### Find + Entire Place
- **Steps**: Basic Info → Extended About You → Rental Preferences
- **Use Case**: Looking for an entire property to rent

## Components

### MultiStepForm
Main form component that orchestrates the entire flow.

**Props:**
- `intent`: "rent" | "find"
- `spaceType`: "private-room" | "entire-place"
- `onSubmit`: (data) => void
- `title`: string (optional)
- `showProgress`: boolean (optional, default: true)
- `showStepInfo`: boolean (optional, default: true)

### useMultiStepForm Hook
Custom hook that manages form state, validation, and navigation.

**Returns:**
- `form`: React Hook Form instance
- `currentStep`: Current step index
- `totalSteps`: Total number of steps
- `nextStep`: Function to go to next step
- `prevStep`: Function to go to previous step
- `validateCurrentStep`: Function to validate current step
- `handleSubmit`: Function to submit the form

### Step Components
Individual step components that render form fields:

- `BasicInfoStep`: Name, email, intent, space type
- `AboutYouStep`: Basic personal information
- `ExtendedAboutYouStep`: Detailed personal information for "find" forms
- `ProfilePhotoStep`: Profile photo upload
- `HomeDetailsStep`: Home/property details for rent forms
- `SpaceDetailsStep`: Space details for entire place rentals
- `RoomDetailsStep`: Room details and rental terms
- `RoomPreferencesStep`: Room preferences for find forms
- `RoommatePreferencesStep`: Roommate preferences
- `TenantPreferencesStep`: Tenant preferences for landlords
- `RentalPreferencesStep`: Rental preferences for entire place searches
- `PhotoUploadStep`: Property/space photo uploads

## Validation Schemas

The system uses Zod for validation with separate schemas for each form type:

- `rentPrivateRoomSchema`: Validation for rent + private room
- `rentEntirePlaceSchema`: Validation for rent + entire place
- `findPrivateRoomSchema`: Validation for find + private room
- `findEntirePlaceSchema`: Validation for find + entire place

## Customization

### Adding New Steps
1. Create a new step component in `steps/`
2. Add the step configuration to `formConfigs.ts`
3. Update the form schema if needed
4. Import and register in `StepRenderer.tsx`

### Modifying Validation
Update the relevant schema in `lib/validations/index.ts` and the step configuration in `formConfigs.ts`.

### Styling
All components use Tailwind CSS classes and can be customized by modifying the className props or updating the component styles.

## Demo

Visit `/multi-step-form-demo` to see the form system in action with all available configurations.

## File Structure

```
components/Forms/MultiStepForm/
├── MultiStepForm.tsx          # Main form component
├── useMultiStepForm.ts        # Form state management hook
├── formConfigs.ts             # Form configurations and step definitions
├── StepRenderer.tsx           # Step component renderer
├── MultiStepFormDemo.tsx      # Demo component
├── steps/                     # Individual step components
│   ├── BasicInfoStep.tsx
│   ├── AboutYouStep.tsx
│   ├── ExtendedAboutYouStep.tsx
│   ├── ProfilePhotoStep.tsx
│   ├── HomeDetailsStep.tsx
│   ├── SpaceDetailsStep.tsx
│   ├── RoomDetailsStep.tsx
│   ├── RoomPreferencesStep.tsx
│   ├── RoommatePreferencesStep.tsx
│   ├── TenantPreferencesStep.tsx
│   ├── RentalPreferencesStep.tsx
│   └── PhotoUploadStep.tsx
├── index.ts                   # Exports
└── README.md                  # This file
```
