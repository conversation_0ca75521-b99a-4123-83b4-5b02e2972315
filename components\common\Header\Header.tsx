import React from "react";
import Link from "next/link";
import Logo from "../Logo";
import LoginForm from "@/components/Forms/LoginForm/LoginForm";
import { headerNavigations, offcanvasNavigations } from "@/data";
import { Navigation, IOffcanvasNavigation } from "@/typescript/interfaces";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

const Header = () => {
  return (
    <header className="bg-white border-b">
      <div className="container">
        <div className="flex items-center gap-5">
          <Logo className="w-40!" variant="dark" />
          <div className="flex flex-1 justify-end md:justify-between items-center gap-4">
            <ul className="hidden md:flex items-center gap-5">
              {headerNavigations.map((navigation: Navigation, idx: number) => (
                <li key={idx}>
                  <Link
                    href={navigation.href}
                    className="text-base lg:text-lg font-semibold text-gray-500 hover:text-secondary"
                  >
                    {navigation.label}
                  </Link>
                </li>
              ))}
            </ul>
            <div className="flex items-center gap-2 md:gap-4 lg:gap-8">
              <Button asChild className="rounded-full">
                <Link href={"#"}>
                  <DynamicIcon name="house-plus" />
                  List Your Space for Free
                </Link>
              </Button>
              <OffcanvasNavigation />
              <MobileNavigation />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

// Off Canvas Navigation
const OffcanvasNavigation = () => {
  return (
    <Sheet>
      <SheetTrigger className="hidden md:block cursor-pointer">
        <DynamicIcon name="align-left" size={34} />
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>
            <Logo className="w-40!" />
          </SheetTitle>
        </SheetHeader>
        <div className="flex flex-col gap-4 px-4 pb-4">
          <ul className="space-y-4">
            {offcanvasNavigations.map((navigation: IOffcanvasNavigation, idx: number) => (
              <li key={idx}>
                <Link
                  href={navigation.href}
                  className="text-lg text-gray-500 hover:text-primary inline-flex items-center gap-4 p-2"
                >
                  <DynamicIcon name={navigation.icon} size={24} />
                  {navigation.label}
                </Link>
              </li>
            ))}
            <li>
              <LoginFormWrapper />
            </li>
          </ul>
        </div>
      </SheetContent>
    </Sheet>
  );
};

const MobileNavigation = () => {
  return (
    <div className="block md:hidden fixed left-0 right-0 bottom-0 p-2 bg-white z-30">
      <ul className="flex items-center justify-between">
        <li>
          <Link
            href={"#"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-gray-700"
          >
            <DynamicIcon name="home" size={20} />
          </Link>
        </li>
        <li>
          <Link
            href={"#"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-gray-700"
          >
            <DynamicIcon name="house-plus" size={20} />
          </Link>
        </li>
        <li>
          <Link
            href={"#"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-gray-700"
          >
            <DynamicIcon name="map-pin-house" size={20} />
          </Link>
        </li>
        <li>
          <Link
            href={"#"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-gray-700"
          >
            <DynamicIcon name="heart" size={20} />
          </Link>
        </li>
        <li>
          <Link
            href={"#"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-gray-700"
          >
            <DynamicIcon name="message-square-share" size={20} />
          </Link>
        </li>
        <li>
          <LoginFormWrapper isMobile={true} />
        </li>
      </ul>
    </div>
  );
};

const LoginFormWrapper = ({ isMobile = false }: { isMobile?: boolean }) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {isMobile ? (
          <button className="w-10 h-10 rounded-md flex items-center justify-center text-gray-700 bg-white">
            <DynamicIcon name="user" size={20} />
          </button>
        ) : (
          <Button className="rounded-full w-full text-lg lg:py-3 h-auto">
            <DynamicIcon name="log-in" size={20} />
            Login
          </Button>
        )}
      </DialogTrigger>
      <DialogContent>
        <DialogTitle className="hidden">Login</DialogTitle>
        <LoginForm />
      </DialogContent>
    </Dialog>
  );
};

export default Header;
