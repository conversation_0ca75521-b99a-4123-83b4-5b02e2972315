"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { quickSearchBarSchema, QuickSearchBarValues } from "@/lib/validations";

interface QuickSearchBarProps {
  className?: string;
}

const QuickSearchBar = ({ className }: QuickSearchBarProps): React.JSX.Element => {
  const [activeTab, setActiveTab] = useState<string>("explore-spaces");

  return (
    <div className={`${className}`}>
      <Tabs defaultValue="explore-spaces" onValueChange={(value: string) => setActiveTab(value)}>
        <TabsList className="bg-transparent h-auto gap-3">
          <TabsTrigger
            value="explore-spaces"
            className="p-2 md:px-4 md:py-3.5 h-auto bg-white! text-secondary text-base data-[state=active]:bg-secondary! data-[state=active]:text-white cursor-pointer"
          >
            <DynamicIcon name="map-pin-house" size={20} />
            Explore Spaces
          </TabsTrigger>
          <TabsTrigger
            value="explore-faces"
            className="p-2 md:px-4 md:py-3.5 h-auto bg-white! text-secondary text-base data-[state=active]:bg-secondary! data-[state=active]:text-white cursor-pointer"
          >
            <DynamicIcon name="user-round-search" size={20} />
            Explore Faces
          </TabsTrigger>
        </TabsList>
        <TabsContent value="explore-spaces">
          <SearchBar activeTab={activeTab} />
        </TabsContent>
        <TabsContent value="explore-faces">
          <SearchBar activeTab={activeTab} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

const SearchBar = ({ activeTab }: { activeTab: string }) => {
  const router = useRouter();
  const form = useForm({
    resolver: zodResolver(quickSearchBarSchema),
    defaultValues: {
      search: "",
    },
  });

  const onSubmit = (data: QuickSearchBarValues) => {
    console.log("Search data:", data);
    // Redirect based on active tab
    if (activeTab === "explore-spaces") {
      router.push(`/explore-spaces?query=${encodeURIComponent(data.search)}`);
    } else if (activeTab === "explore-faces") {
      router.push(`/explore-faces?query=${encodeURIComponent(data.search)}`);
    } else {
      return;
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          name="search"
          control={form.control}
          render={({ field }) => (
            <FormItem className="relative">
              <FormControl>
                <Input
                  {...field}
                  className="bg-white pl-2 pr-24 py-2 md:pl-6 md:pr-28 md:py-4 h-auto"
                  placeholder="Enter an address, neighborhood city or zip/postal code"
                />
              </FormControl>
              <Button
                type="submit"
                className="absolute top-0 right-0 bottom-0 p-[22px] md:p-[27px] rounded-bl-none rounded-tl-none cursor-pointer"
              >
                Search
              </Button>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
};

export default QuickSearchBar;
