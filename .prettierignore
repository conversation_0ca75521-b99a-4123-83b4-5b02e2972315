# Next.js
.next
out
# Ignore artifacts:
build
coverage
# Ignore build:
dist
node_modules
package.json
package.lock.json
# Ignore generated files:
*.min.js
*.min.js.map
# Ignore tests:
test
tests
# Ignore IDEs:
*.iml
*.idea
*.vscode
*.sln
*.suo
*.user
*.csproj
*.vcxproj
*.vcxproj.filters
*.filters
# Ignore misc:
.DS_Store
*.swp
*.swo
*.swx
*.lock
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
pnpm-lock.yaml
