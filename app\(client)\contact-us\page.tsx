import React from "react";
import Link from "next/link";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import ContactUsForm from "./_components/ContactUsForm";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { socialLinks } from "@/data";
import { ISocialLink } from "@/typescript/interfaces";

const ContactUsPage = () => {
  return (
    <>
      <TopBanner title="Contact Us" />
      <section>
        <div className="container">
          <div className="grid grid-cols-12 gap-5">
            <div className="space-y-8 col-span-12 lg:col-span-8">
              <div className="space-y-4">
                <h2 className="text-3xl lg:text-4xl font-semibold text-secondary">Get in Touch</h2>
                <p className="text-gray-700 text-base leading-relaxed">
                  Our team is here to assist you 24 hours a day, 7 days a week. Please send us a
                  message and we&apos;ll respond ASAP.
                </p>
              </div>
              <ContactUsForm />
            </div>
            <Card className="col-span-12 lg:col-span-4">
              <CardHeader>
                <CardTitle className="text-2xl text-secondary font-semibold">Contact Us</CardTitle>
              </CardHeader>
              <CardContent className="space-y-5">
                <div className="space-y-2">
                  <h4 className="text-lg font-semibold text-secondary flex items-center gap-1.5">
                    <DynamicIcon name="mail" size={18} /> Email Address:
                  </h4>
                  <Link href="mailto:<EMAIL>" className="text-gray-700">
                    <EMAIL>
                  </Link>
                </div>
                <div className="space-y-2">
                  <h4 className="text-lg font-semibold text-secondary flex items-center gap-1.5">
                    <DynamicIcon name="phone" size={18} />
                    Phone Number:
                  </h4>
                  <Link href="tel:(*************" className="text-gray-700">
                    (*************
                  </Link>
                </div>
                <div className="space-y-2">
                  <h4 className="text-lg font-semibold text-secondary flex items-center gap-1.5">
                    Follow Us:
                  </h4>
                  <ul className="flex items-center gap-4">
                    {socialLinks.map((socialLink: ISocialLink, idx: number) => (
                      <li key={idx}>
                        <Link
                          href={socialLink.href}
                          title={socialLink.label}
                          className="block border border-gray-600 text-gray-600 p-3 rounded-full"
                        >
                          <socialLink.icon size={18} />
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </>
  );
};

export default ContactUsPage;
