import { z } from "zod";
import {
  basicInfoSchema,
  profilePhotoSchema,
  basicAboutYouSchema,
  extendedAboutYouSchema,
  homeDetailsSchema,
  spaceDetailsSchema,
  roomRentDetailsSchema,
  roomFindDetailsSchema,
  spacePhotosSchema,
  roommatePreferencesSchema,
  findRoommatePreferencesSchema,
  rentalBasicsSchema,
  tenantPreferencesSchema,
  rentalPreferencesSchema,
  rentPrivateRoomSchema,
  rentEntirePlaceSchema,
  findPrivateRoomSchema,
  findEntirePlaceSchema,
} from "@/lib/validations";

export type FormIntent = "rent" | "find";
export type SpaceType = "private-room" | "entire-place";

export interface StepConfig {
  id: string;
  title: string;
  description?: string;
  schema: z.ZodSchema;
  fields: readonly string[];
  component: string;
}

export interface FormConfig {
  intent: FormIntent;
  spaceType: SpaceType;
  schema: z.ZodSchema;
  steps: StepConfig[];
}

// Step configurations
export const stepConfigs = {
  basicInfo: {
    id: "basic-info",
    title: "Basic Information",
    description: "Tell us about yourself and what you're looking for",
    schema: basicInfoSchema,
    fields: ["firstName", "lastName", "email", "intent", "spaceType"],
    component: "BasicInfoStep",
  },
  profilePhoto: {
    id: "profile-photo",
    title: "Profile Photo",
    description: "Upload your profile photo",
    schema: profilePhotoSchema,
    fields: ["profilePhoto"],
    component: "ProfilePhotoStep",
  },
  basicAboutYou: {
    id: "basic-about-you",
    title: "About You",
    description: "Tell us more about yourself",
    schema: z.object({ aboutYou: basicAboutYouSchema }),
    fields: ["aboutYou"],
    component: "AboutYouStep",
  },
  extendedAboutYou: {
    id: "extended-about-you",
    title: "About You",
    description: "Tell us more about yourself and preferences",
    schema: z.object({ aboutYou: extendedAboutYouSchema }),
    fields: ["aboutYou"],
    component: "ExtendedAboutYouStep",
  },
  extendedAboutYouNoMarijuana: {
    id: "extended-about-you-no-marijuana",
    title: "About You",
    description: "Tell us more about yourself and preferences",
    schema: z.object({ aboutYou: extendedAboutYouSchema.omit({ smokeMarijuana: true }) }),
    fields: ["aboutYou"],
    component: "ExtendedAboutYouStep",
  },
  homeDetails: {
    id: "home-details",
    title: "The Home",
    description: "Describe your home and its features",
    schema: z.object({ home: homeDetailsSchema }),
    fields: ["home"],
    component: "HomeDetailsStep",
  },
  spaceDetails: {
    id: "space-details",
    title: "The Space",
    description: "Describe your space and its features",
    schema: z.object({ space: spaceDetailsSchema }),
    fields: ["space"],
    component: "SpaceDetailsStep",
  },
  roomRentDetails: {
    id: "room-rent-details",
    title: "The Room",
    description: "Describe the room and rental terms",
    schema: z.object({ room: roomRentDetailsSchema }),
    fields: ["room"],
    component: "RoomDetailsStep",
  },
  roomFindDetails: {
    id: "room-find-details",
    title: "Room Preferences",
    description: "Tell us about your room preferences",
    schema: z.object({ room: roomFindDetailsSchema }),
    fields: ["room"],
    component: "RoomPreferencesStep",
  },
  spacePhotos: {
    id: "space-photos",
    title: "Photos",
    description: "Upload photos of your space",
    schema: spacePhotosSchema,
    fields: ["spacePhotos"],
    component: "PhotoUploadStep",
  },
  roommatePreferences: {
    id: "roommate-preferences",
    title: "Roommate Preferences",
    description: "Describe your ideal roommate",
    schema: z.object({ roommatePreferences: roommatePreferencesSchema }),
    fields: ["roommatePreferences"],
    component: "RoommatePreferencesStep",
  },
  findRoommatePreferences: {
    id: "find-roommate-preferences",
    title: "Roommate Preferences",
    description: "Describe your roommate preferences",
    schema: z.object({ roommatePreferences: findRoommatePreferencesSchema }),
    fields: ["roommatePreferences"],
    component: "FindRoommatePreferencesStep",
  },
  rentalBasics: {
    id: "rental-basics",
    title: "Rental Basics",
    description: "Set your rental terms and conditions",
    schema: z.object({ rentalBasics: rentalBasicsSchema }),
    fields: ["rentalBasics"],
    component: "RentalBasicsStep",
  },
  tenantPreferences: {
    id: "tenant-preferences",
    title: "Tenant Preferences",
    description: "Describe your ideal tenant",
    schema: z.object({ tenantPreferences: tenantPreferencesSchema }),
    fields: ["tenantPreferences"],
    component: "TenantPreferencesStep",
  },
  rentalPreferences: {
    id: "rental-preferences",
    title: "Rental Preferences",
    description: "Tell us about your rental preferences",
    schema: z.object({ rentalPreferences: rentalPreferencesSchema }),
    fields: ["rentalPreferences"],
    component: "RentalPreferencesStep",
  },
} as const;

// Form configurations for different intent/spaceType combinations
export const formConfigs: Record<string, FormConfig> = {
  "rent-private-room": {
    intent: "rent",
    spaceType: "private-room",
    schema: rentPrivateRoomSchema,
    steps: [
      stepConfigs.basicInfo,
      {
        id: "about-you-profile",
        title: "About You & Profile",
        description: "Tell us about yourself and upload your photo",
        schema: z.object({ aboutYou: basicAboutYouSchema }).merge(profilePhotoSchema),
        fields: ["aboutYou", "profilePhoto"],
        component: "AboutYouStep",
      },
      stepConfigs.homeDetails,
      {
        id: "room-details-photos",
        title: "Room Details & Photos",
        description: "Describe the room and upload photos",
        schema: z.object({ room: roomRentDetailsSchema }).merge(spacePhotosSchema),
        fields: ["room", "spacePhotos"],
        component: "RoomDetailsStep",
      },
      stepConfigs.roommatePreferences,
    ],
  },
  "rent-entire-place": {
    intent: "rent",
    spaceType: "entire-place",
    schema: rentEntirePlaceSchema,
    steps: [
      stepConfigs.basicInfo,
      {
        id: "about-you-profile",
        title: "About You & Profile",
        description: "Tell us about yourself and upload your photo",
        schema: z.object({ aboutYou: basicAboutYouSchema }).merge(profilePhotoSchema),
        fields: ["aboutYou", "profilePhoto"],
        component: "AboutYouStep",
      },
      {
        id: "space-details-photos",
        title: "Space Details & Photos",
        description: "Describe your space and upload photos",
        schema: z.object({ space: spaceDetailsSchema }).merge(spacePhotosSchema),
        fields: ["space", "spacePhotos"],
        component: "SpaceDetailsStep",
      },
      stepConfigs.rentalBasics,
      stepConfigs.tenantPreferences,
    ],
  },
  "find-private-room": {
    intent: "find",
    spaceType: "private-room",
    schema: findPrivateRoomSchema,
    steps: [
      stepConfigs.basicInfo,
      {
        id: "extended-about-you-profile",
        title: "About You & Profile",
        description: "Tell us about yourself and upload your photo",
        schema: z
          .object({ aboutYou: extendedAboutYouSchema.omit({ smokeMarijuana: true }) })
          .merge(profilePhotoSchema),
        fields: ["aboutYou", "profilePhoto"],
        component: "ExtendedAboutYouStep",
      },
      stepConfigs.roomFindDetails,
      stepConfigs.findRoommatePreferences,
    ],
  },
  "find-entire-place": {
    intent: "find",
    spaceType: "entire-place",
    schema: findEntirePlaceSchema,
    steps: [
      stepConfigs.basicInfo,
      {
        id: "extended-about-you-profile",
        title: "About You & Profile",
        description: "Tell us about yourself and upload your photo",
        schema: z.object({ aboutYou: extendedAboutYouSchema }).merge(profilePhotoSchema),
        fields: ["aboutYou", "profilePhoto"],
        component: "ExtendedAboutYouStep",
      },
      stepConfigs.rentalPreferences,
    ],
  },
};

export const getFormConfig = (intent: FormIntent, spaceType: SpaceType): FormConfig => {
  const key = `${intent}-${spaceType}`;
  const config = formConfigs[key];
  if (!config) {
    throw new Error(`No form configuration found for ${intent} + ${spaceType}`);
  }
  return config;
};

export const getStepFields = (config: FormConfig, stepIndex: number): readonly string[] => {
  if (stepIndex < 0 || stepIndex >= config.steps.length) {
    return [];
  }
  return config.steps[stepIndex].fields;
};
