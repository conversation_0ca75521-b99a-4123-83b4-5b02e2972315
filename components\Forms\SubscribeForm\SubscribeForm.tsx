"use client";

import React from "react";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { subscriptionFormSchema, SubscriptionFormValues } from "@/lib/validations";

const SubscribeForm = () => {
  const form = useForm({
    resolver: zodResolver(subscriptionFormSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = (data: SubscriptionFormValues) => {
    console.log("Subscription form data:", data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="relative">
              <FormControl className="bg-white text-gray-800">
                <Input
                  placeholder="Your email"
                  {...field}
                  className="w-full h-auto py-5 rounded-full"
                />
              </FormControl>
              <Button
                type="submit"
                className="absolute right-[7px] top-[7px] md:right-[5px] md:top-[5px] h-auto py-4 rounded-full"
              >
                Subscribe
              </Button>
              <FormDescription className="text-white text-sm md:text-base">
                Subscribe to our newsletter to receive our weekly feed.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
};

export default SubscribeForm;
