"use client";

import React from "react";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useMultiStepForm, UseMultiStepFormProps } from "./useMultiStepForm";
import { StepRenderer } from "./StepRenderer";

export interface MultiStepFormProps extends UseMultiStepFormProps {
  title?: string;
  className?: string;
  showProgress?: boolean;
  showStepInfo?: boolean;
}

export const MultiStepForm: React.FC<MultiStepFormProps> = ({
  title = "Multi-Step Form",
  className = "",
  showProgress = true,
  showStepInfo = true,
  ...formProps
}) => {
  const {
    form,
    currentStep,
    totalSteps,
    isFirstStep,
    isLastStep,
    nextStep,
    prevStep,
    config,
    currentStepConfig,
    handleSubmit,
    progress,
  } = useMultiStepForm(formProps);

  if (!config || !currentStepConfig) {
    return (
      <div className="max-w-3xl mx-auto p-6 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Form Configuration Error</h2>
          <p className="text-gray-600">
            Unable to load form configuration. Please check your intent and space type.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`max-w-3xl mx-auto p-6 bg-white rounded-lg shadow-md ${className}`}>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-2">{title}</h2>
        
        {/* Progress Bar */}
        {showProgress && (
          <div className="mb-4">
            <Progress value={progress} className="h-2" />
          </div>
        )}
        
        {/* Step Info */}
        {showStepInfo && (
          <div className="space-y-2">
            <p className="text-gray-600">
              Step {currentStep + 1} of {totalSteps}
            </p>
            <div>
              <h3 className="text-lg font-semibold">{currentStepConfig.title}</h3>
              {currentStepConfig.description && (
                <p className="text-sm text-gray-500">{currentStepConfig.description}</p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Step Content */}
          <StepRenderer
            form={form}
            stepConfig={currentStepConfig}
            intent={config.intent}
            spaceType={config.spaceType}
          />

          {/* Navigation */}
          <div className="flex justify-between mt-8 pt-6 border-t">
            {!isFirstStep ? (
              <Button type="button" variant="outline" onClick={prevStep}>
                Previous
              </Button>
            ) : (
              <div />
            )}
            
            {!isLastStep ? (
              <Button type="button" onClick={nextStep}>
                Next
              </Button>
            ) : (
              <Button type="submit">
                Submit
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
};

export default MultiStepForm;
