import React from "react";
import { UseFormReturn, FieldValues } from "react-hook-form";

interface RoommatePreferencesStepProps {
  form: UseFormReturn<FieldValues>;
}

const RoommatePreferencesStep: React.FC<RoommatePreferencesStepProps> = () => {
  return (
    <div className="space-y-6">
      <div className="text-center py-8">
        <h3 className="text-lg font-semibold mb-2">Roommate Preferences Step</h3>
        <p className="text-gray-600">This component is under development</p>
      </div>
    </div>
  );
};

export default RoommatePreferencesStep;
