"use client";

import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css/navigation";

const ListingImagesSlide = ({ images }: { images: string[] }) => {
  return (
    <Swiper className="w-full" navigation={true} modules={[Navigation]}>
      {images.map((image: string, idx: number) => (
        <SwiperSlide key={idx}>
          <Image
            src={image}
            alt={`Image ${idx}`}
            width={800}
            height={800}
            priority
            className="w-full h-full rounded-lg"
          />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default ListingImagesSlide;
