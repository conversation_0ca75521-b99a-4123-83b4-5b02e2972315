"use client";

import React from "react";
import Link from "next/link";
import Logo from "@/components/common/Logo";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { FaInstagram, FaFacebook, FaGoogle } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { zodResolver } from "@hookform/resolvers/zod";
import { loginFormSchema, LoginFormValues } from "@/lib/validations";

const LoginForm = () => {
  const form = useForm({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  const onSubmit = (data: LoginFormValues) => {
    console.log("Login form data:", data);
  };

  return (
    <div>
      <div className="text-center space-y-4 mb-6">
        <Logo className="w-40! mx-auto mb-4" />
        <h2 className="text-3xl font-bold text-gray-600">Log In</h2>
        <p className="text-gray-600">
          Don’t have an account?{" "}
          <Link href={"/auth/register"} className="text-secondary underline">
            Sign Up
          </Link>
        </p>
      </div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            name="email"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">
                  Email <span className="field-required" />
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    className="input-field"
                    placeholder="<EMAIL>"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="password"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">
                  Password <span className="field-required" />
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="password"
                    className="input-field"
                    placeholder="********"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="flex items-center justify-between">
            <FormField
              name="rememberMe"
              control={form.control}
              render={({ field }) => (
                <FormItem className="flex flex-row">
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormLabel className="font-normal!">Remember me</FormLabel>
                </FormItem>
              )}
            />
            <Link href={"#"} className="text-gray-600 underline">
              Forgot your password
            </Link>
            <FormMessage />
          </div>
          <Button type="submit" className="w-full h-auto py-3 rounded-full text-lg cursor-pointer">
            Login
          </Button>
          <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
            <span className="relative z-10 bg-background px-2 text-muted-foreground">OR</span>
          </div>
          <div className="space-y-4">
            <Button
              type="button"
              variant="outline"
              className="w-full h-auto py-3 rounded-full text-lg cursor-pointer"
            >
              <FaGoogle size={20} />
              Login with Google
            </Button>
            <Button
              type="button"
              variant="outline"
              className="w-full h-auto py-3 rounded-full text-lg cursor-pointer"
            >
              <FaInstagram size={20} />
              Log In with Instagram
            </Button>
            <Button
              type="button"
              variant="outline"
              className="w-full h-auto py-3 rounded-full text-lg cursor-pointer"
            >
              <FaFacebook size={20} />
              Login with Facebook
            </Button>
            <Button
              type="button"
              variant="outline"
              className="w-full h-auto py-3 rounded-full text-lg cursor-pointer"
            >
              <FaXTwitter size={20} />
              Login with X
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default LoginForm;
