import React from "react";
import { UseFormReturn } from "react-hook-form";

interface SpaceDetailsStepProps {
  form: UseFormReturn<any>;
}

const SpaceDetailsStep: React.FC<SpaceDetailsStepProps> = ({ form }) => {
  return (
    <div className="space-y-6">
      <div className="text-center py-8">
        <h3 className="text-lg font-semibold mb-2">Space Details Step</h3>
        <p className="text-gray-600">This component is under development</p>
      </div>
    </div>
  );
};

export default SpaceDetailsStep;
