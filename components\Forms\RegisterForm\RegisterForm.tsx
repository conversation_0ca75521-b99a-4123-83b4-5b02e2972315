"use client";

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  findEntirePlaceSchema,
  findPrivateRoomSchema,
  propertyCommonSchema,
  PropertyCommonValues,
  rentEntirePlaceSchema,
  rentPrivateRoomSchema,
} from "@/lib/validations";

// Step components
import BasicInfoStep from "./steps/BasicInfoStep";
import AboutYouStep from "./steps/AboutYouStep";
import HomeDetailsStep from "./steps/HomeDetailsStep";
import RoomDetailsStep from "./steps/RoomDetailsStep";
import SpaceDetailsStep from "./steps/SpaceDetailsStep";
import RentalBasicsStep from "./steps/RentalBasicsStep";
import RoommatePreferencesStep from "./steps/RoommatePreferencesStep";
import TenantPreferencesStep from "./steps/TenantPreferencesStep";
import RentalPreferencesStep from "./steps/RentalPreferencesStep";
import PhotoUploadStep from "./steps/PhotoUploadStep";

const RegisterForm = () => {
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [totalSteps, setTotalSteps] = useState<number>(5);
  const [schema, setSchema] = useState(propertyCommonSchema);
  
  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      intent: "rent",
      spaceType: "private-room",
      aboutYou: {
        genderIdentity: "",
        sexualOrientation: "",
        age: undefined,
        smokeCigarettes: false,
        smokeMarijuana: false,
        workFromHome: false,
        travel: "",
        cleanliness: "",
        selfDescription: "",
        zodiac: "",
        fluentLanguages: [],
        describeMyself: "",
      },
      profilePhoto: undefined,
      home: {
        address: "",
        residenceType: "",
        size: undefined,
        bedrooms: undefined,
        bathrooms: undefined,
        ownerOccupied: false,
        numberOfPeople: undefined,
        amenities: [],
        parking: "",
        neighborhood: "",
        currentPets: "",
        allowedPets: "",
        cigaretteSmoking: false,
        marijuanaSmoking: false,
        overnightGuests: false,
        homeDescription: "",
      },
      room: {
        availabilityDate: "",
        availabilityDuration: "",
        minimumDuration: "",
        monthlyRent: undefined,
        depositAmount: undefined,
        leaseRequired: false,
        requiredReferences: "",
        utilitiesIncluded: false,
        furnished: false,
        bedroomSize: "",
        brightness: "",
        bathroom: "",
        roomFeatures: [],
      },
      space: {
        address: "",
        residenceType: "",
        size: undefined,
        bedrooms: undefined,
        bathrooms: undefined,
        brightness: "",
        amenities: [],
        parking: "",
        neighborhood: "",
        allowedPets: "",
        cigaretteSmoking: false,
        marijuanaSmoking: false,
        homeDescription: "",
      },
      rentalBasics: {
        availabilityDate: "",
        availabilityDuration: "",
        minimumDuration: "",
        monthlyRent: undefined,
        depositAmount: undefined,
        leaseRequired: false,
        requiredReferences: "",
        utilitiesIncluded: false,
        furnished: false,
      },
      roommatePreferences: {
        genderIdentity: "",
        sexualOrientation: "",
        ageRange: "",
        smokingHabits: "",
        idealRoommate: "",
      },
      tenantPreferences: {
        idealTenant: "",
      },
      rentalPreferences: {
        preferredLocation: "",
        rentalStartDate: "",
        rentalDuration: "",
        maxMonthlyBudget: undefined,
        rentalAgreement: false,
        furnished: false,
        bedrooms: undefined,
        bathrooms: undefined,
        pets: "",
        parkingRequired: false,
      },
      spacePhotos: undefined,
    },
  });

  const { watch, handleSubmit, trigger } = form;
  const intent = watch("intent");
  const spaceType = watch("spaceType");

  // Update schema and total steps based on intent and spaceType
  useEffect(() => {
    if (intent === "rent" && spaceType === "private-room") {
      setSchema(rentPrivateRoomSchema);
      setTotalSteps(5); // Basic, About You, Home, Room, Roommate Prefs
    } else if (intent === "rent" && spaceType === "entire-place") {
      setSchema(rentEntirePlaceSchema);
      setTotalSteps(5); // Basic, About You, Space, Rental Basics, Tenant Prefs
    } else if (intent === "find" && spaceType === "private-room") {
      setSchema(findPrivateRoomSchema);
      setTotalSteps(4); // Basic, About You, Room, Roommate Prefs
    } else if (intent === "find" && spaceType === "entire-place") {
      setSchema(findEntirePlaceSchema);
      setTotalSteps(4); // Basic, About You, Rental Prefs
    }
  }, [intent, spaceType]);

  const nextStep = async () => {
    // Validate current step fields before proceeding
    const fieldsToValidate = getFieldsForStep(currentStep);
    const isValid = await trigger(fieldsToValidate);
    
    if (isValid) {
      setCurrentStep((prev) => Math.min(prev + 1, totalSteps));
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const getFieldsForStep = (step: number): string[] => {
    // Common first step for all forms
    if (step === 1) {
      return ["firstName", "lastName", "email", "intent", "spaceType"];
    }
    
    // About You step
    if (step === 2) {
      return ["aboutYou", "profilePhoto"];
    }
    
    // Conditional steps based on intent and spaceType
    if (intent === "rent") {
      if (spaceType === "private-room") {
        if (step === 3) return ["home"];
        if (step === 4) return ["room", "spacePhotos"];
        if (step === 5) return ["roommatePreferences"];
      } else { // entire-place
        if (step === 3) return ["space", "spacePhotos"];
        if (step === 4) return ["rentalBasics"];
        if (step === 5) return ["tenantPreferences"];
      }
    } else { // find
      if (spaceType === "private-room") {
        if (step === 3) return ["room"];
        if (step === 4) return ["roommatePreferences"];
      } else { // entire-place
        if (step === 3) return ["rentalPreferences"];
      }
    }
    
    return [];
  };

  const onSubmit = (data: any) => {
    console.log("Form Data:", data);
    alert("Form submitted successfully!");
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <BasicInfoStep form={form} />;
      case 2:
        return <AboutYouStep form={form} intent={intent} spaceType={spaceType} />;
      case 3:
        if (intent === "rent" && spaceType === "private-room") {
          return <HomeDetailsStep form={form} />;
        } else if (intent === "rent" && spaceType === "entire-place") {
          return <SpaceDetailsStep form={form} />;
        } else if (intent === "find" && spaceType === "private-room") {
          return <RoomDetailsStep form={form} isFind={true} />;
        } else {
          return <RentalPreferencesStep form={form} />;
        }
      case 4:
        if (intent === "rent" && spaceType === "private-room") {
          return <RoomDetailsStep form={form} isFind={false} />;
        } else if (intent === "rent" && spaceType === "entire-place") {
          return <RentalBasicsStep form={form} />;
        } else if (intent === "find" && spaceType === "private-room") {
          return <RoommatePreferencesStep form={form} />;
        } else {
          // Last step for Find + Entire Place
          return null;
        }
      case 5:
        if (intent === "rent" && spaceType === "private-room") {
          return <RoommatePreferencesStep form={form} />;
        } else if (intent === "rent" && spaceType === "entire-place") {
          return <TenantPreferencesStep form={form} />;
        }
        return null;
      default:
        return null;
    }
  };

  return (
    <div className="max-w-3xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-2">Register Your Space</h2>
        <div className="flex items-center justify-between mb-4">
          {Array.from({ length: totalSteps }).map((_, idx) => (
            <div 
              key={idx} 
              className={`h-2 flex-1 mx-1 rounded-full ${
                idx + 1 <= currentStep ? "bg-primary" : "bg-gray-200"
              }`}
            />
          ))}
        </div>
        <p className="text-gray-600">
          Step {currentStep} of {totalSteps}
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {renderStepContent()}

          <div className="flex justify-between mt-8">
            {currentStep > 1 && (
              <Button type="button" variant="outline" onClick={prevStep}>
                Previous
              </Button>
            )}
            
            {currentStep < totalSteps ? (
              <Button type="button" onClick={nextStep} className="ml-auto">
                Next
              </Button>
            ) : (
              <Button type="submit" className="ml-auto">
                Submit
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
};

export default RegisterForm;
