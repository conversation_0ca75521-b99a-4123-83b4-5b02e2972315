import React from "react";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { UseFormReturn, FieldValues } from "react-hook-form";

interface BasicInfoStepProps {
  form: UseFormReturn<FieldValues>;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ form }) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                First Name <span className="field-required text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input {...field} className="input-field" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Last Name <span className="field-required text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input {...field} className="input-field" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="input-label">
              Email <span className="field-required text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <Input {...field} type="email" className="input-field" />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="intent"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="input-label">
              I want to <span className="field-required text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-col space-y-2"
              >
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="rent" />
                  </FormControl>
                  <FormLabel className="font-normal cursor-pointer">Rent available space</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="find" />
                  </FormControl>
                  <FormLabel className="font-normal cursor-pointer">Find available space</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="spaceType"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="input-label">
              Space Type <span className="field-required text-red-500">*</span>
            </FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-col space-y-2"
              >
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="private-room" />
                  </FormControl>
                  <FormLabel className="font-normal cursor-pointer">Private Room</FormLabel>
                </FormItem>
                <FormItem className="flex items-center space-x-3 space-y-0">
                  <FormControl>
                    <RadioGroupItem value="entire-place" />
                  </FormControl>
                  <FormLabel className="font-normal cursor-pointer">Entire Place</FormLabel>
                </FormItem>
              </RadioGroup>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default BasicInfoStep;
