import React from "react";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { faqs } from "@/data";
import { FAQ } from "@/typescript/types";

const FaqPage = () => {
  return (
    <>
      <TopBanner title="Frequently Asked Questions" />
      <section>
        <div className="container">
          <Accordion type="single" collapsible className="space-y-5 text-gray-700">
            {faqs.map((faq: FAQ, idx: number) => (
              <AccordionItem key={idx} value={`item-${idx}`} className="px-4 border rounded-lg">
                <AccordionTrigger className="text-base md:text-lg hover:no-underline cursor-pointer">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-base md:text-lg px-4">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </section>
    </>
  );
};

export default FaqPage;
