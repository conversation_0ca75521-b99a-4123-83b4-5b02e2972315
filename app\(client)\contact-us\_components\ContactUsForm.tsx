"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { contactUsFormSchema, ContactUsFormValues } from "@/lib/validations";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const ContactUsForm = () => {
  const form = useForm({
    resolver: zodResolver(contactUsFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: "",
    },
  });

  const onSubmit = (data: ContactUsFormValues) => {
    console.log("Contact us form data:", data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
        <div className="flex flex-col lg:flex-row gap-4">
          <FormField
            name="name"
            control={form.control}
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="input-label">
                  Name <span className="field-required" />
                </FormLabel>
                <FormControl>
                  <Input {...field} placeholder="John Doe" type="text" className="input-field" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="email"
            control={form.control}
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="input-label">
                  Email <span className="field-required" />
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="<EMAIL>"
                    type="email"
                    className="input-field"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="flex flex-col lg:flex-row gap-4">
          <FormField
            name="phone"
            control={form.control}
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="input-label">
                  Phone Number <span className="field-required" />
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="+************"
                    type="tel"
                    className="input-field"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="subject"
            control={form.control}
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel className="input-label">
                  Subject
                  <span className="field-required" />
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Your Subject"
                    type="text"
                    className="input-field"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          name="message"
          control={form.control}
          render={({ field }) => (
            <FormItem className="w-full">
              <FormLabel className="input-label">
                Message <span className="field-required" />
              </FormLabel>
              <FormControl>
                <Textarea {...field} placeholder="Message" className="min-h-44 bg-white" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button className="w-full h-auto py-3 rounded-full cursor-pointer">Send Message</Button>
      </form>
    </Form>
  );
};

export default ContactUsForm;
