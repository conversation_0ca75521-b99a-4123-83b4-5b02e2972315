import React from "react";
import Link from "next/link";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";

const NotFound = () => {
  return (
    <section className="h-screen flex justify-center items-center md:items-start p-0">
      <div className="container">
        <div className="text-center space-y-5">
          <Image
            src={"/images/404.webp"}
            alt="404 Not Found"
            width={1000}
            height={1000}
            priority
            className="mx-auto"
          />
          <h2 className="text-3xl md:text-4xl font-bold">Opps! Page Not Found</h2>
          <div className="flex justify-center gap-4">
            <Button asChild className="rounded-full">
              <Link href="/" aria-label="Go back to the homepage">
                Back To Home
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NotFound;
