import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

const CTA = () => {
  return (
    <section className="py-8 xl:py-16 bg-[url('/images/home/<USER>')]">
      <div className="container">
        <div className="space-y-5 w-full lg:w-1/3">
          <h2 className="text-3xl xl:text-4xl font-semibold text-gray-50">Find your best Match</h2>
          <p className="text-gray-50 text-base leading-relaxed">
            Enjoy #InstantBenefits! No points redemption and tracking points. Exclusive services
            specially designed to enhance the member experience with every visit.
          </p>
          <Button
            asChild
            className="px-8! py-3 w-auto h-auto rounded-full bg-white hover:bg-gray-200 text-gray-800"
          >
            <Link href={"#"}>List Your Space For Free</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CTA;
