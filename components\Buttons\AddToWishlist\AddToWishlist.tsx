"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { FaRegHeart } from "react-icons/fa";

interface AddToWishlistProps {
  className?: string;
}

const AddToWishlist = ({ className }: AddToWishlistProps) => {
  return (
    <Button variant={"outline"} size={"icon"} className={`${className}`}>
      <FaRegHeart />
    </Button>
  );
};

export default AddToWishlist;
