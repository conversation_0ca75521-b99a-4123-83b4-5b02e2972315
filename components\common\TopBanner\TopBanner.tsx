import React from "react";

interface TopBannerProps {
  title: string;
  subtitle?: string;
}

const TopBanner = ({ title, subtitle }: TopBannerProps): React.JSX.Element => {
  return (
    <header className="bg-secondary py-7 text-white text-center">
      <div className="container">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">{title}</h1>
          {subtitle && <p className="">{subtitle}</p>}
        </div>
      </div>
    </header>
  );
};

export default TopBanner;
