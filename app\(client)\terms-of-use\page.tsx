import React from "react";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import { termsOfUseContents } from "@/data";
import { TermsOfUse } from "@/typescript/types";

const TermsOfUsePage = () => {
  return (
    <>
      <TopBanner title="Terms of Use" />
      <section>
        <div className="container">
          <div className="space-y-2 text-gray-700">
            <p className="font-semibold">
              In effect for Gayroom8 Members registered on the Platform after April 27, 2023.
            </p>
            <p>Last update: May 25, 2023</p>
          </div>
          <hr className="my-5" />
          <div className="text-gray-700 space-y-5">
            <p>
              Please read these Terms and Conditions of services carefully. By accessing and using
              the Gayroom8.com Platform you agree to comply with and be legally bound by the terms
              and conditions of these Terms and Conditions of services. These Terms and Conditions
              of services contain a binding arbitration provision and class action waiver that apply
              to all Gayroom8 Members and affect their rights. By accepting these terms, you agree
              to be bound by these provisions. If you do not agree to these Terms and Conditions of
              services, you have no right to obtain information from or otherwise continue using the
              Platform (as defined below). You acknowledge and agree that, by accessing or using the
              Platform or the Content, or by uploading Member Content, you are indicating that you
              have read, and that you understand and agree to be bound by these Terms and Conditions
              of services, whether or not you have registered with the Site and Application. These
              Terms and Conditions of services (&quot;Terms&quot;) constitute a binding legal
              agreement between you and Gayroom8 (as defined below) and to govern your access to,
              and use of, the Gayroom8 website (www.gayroom8.com) including any subdomains thereof,
              and any other websites through which Gayroom8 makes its services available
              (individually and collectively, &quot;Site&quot;), the mobile, tablet and other smart
              device applications, and application program interfaces through which Gayroom8 makes
              its services available (individually and collectively, “Application”) and all
              associated services, (collectively, “Services”). The Site, Application and Services
              together are hereinafter collectively referred to as the “Platform”. The terms “you”
              and “your” refer to you as an individual. If you agree to these Terms on behalf of a
              company or other legal entity, you represent and warrant that you have the authority
              to bind that company or other legal entity to these Terms and, in such event, “you”
              and “your” will refer and apply to that company or other legal entity, unless
              otherwise specified. When these Terms mention “Gayroom8”, it refers to the
              gayroom8.com entity you are contracting with in regards with the access to and use of
              the Platform: GAYROOM8.COM, LLC at 1610 Meridian Ave, Ste. 1, Miami Beach, FL 33139,
              USA. The collection, use and any processing of personal information in connection with
              your access to and use of the Platform is described in the Gayroom8 Privacy Policy. If
              you have any questions about these Terms, you can contact the Gayroom8 Customer
              Support <NAME_EMAIL>.
            </p>
            {termsOfUseContents.map((content: TermsOfUse, idx: number) => (
              <TermsOfUseContent key={idx} {...content} />
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

const TermsOfUseContent = ({ title, contents }: TermsOfUse) => {
  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold">{title}</h3>
      <ul className="space-y-3">
        {contents.map((content: string, idx: number) => (
          <li key={idx}>{content}</li>
        ))}
      </ul>
    </div>
  );
};

export default TermsOfUsePage;
