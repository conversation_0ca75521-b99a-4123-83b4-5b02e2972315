import React from "react";
import HeroSection from "./_components/layout/HeroSection";
import FeaturedSection from "./_components/layout/FeaturedSection";
import CTA from "./_components/layout/CTA";
import WhySection from "./_components/layout/WhySection";

const HomePage = (): React.JSX.Element => {
  return (
    <>
      <HeroSection />
      <FeaturedSection />
      <CTA />
      <WhySection
        title="Why Choose gayroom8?"
        description="Lorem ipsum dolor sit amet consectetur adipiscing elit eu"
        image="/images/home/<USER>"
        points={[
          {
            icon: "check",
            text: "100% Comfortable",
          },
          {
            icon: "check",
            text: "Wide Range of Properties",
          },
          {
            icon: "check",
            text: "Buy or Rent Homes",
          },
          {
            icon: "check",
            text: "Trusted by Thousands",
          },
        ]}
        buttonText="Sign Up for Free"
        buttonLink="#"
      />
    </>
  );
};

export default HomePage;
