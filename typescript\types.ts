import { dynamicIconImports } from "lucide-react/dynamic";
export type TFeaturedCard = {
  icon: keyof typeof dynamicIconImports;
  title: string;
  description: string;
  image: string;
  reverse?: boolean;
};

export type THeroContent = {
  title: string;
  description: string;
  image: string;
};

export type TermsOfUse = {
  title: string;
  contents: string[];
};

export type PrivacyPolicy = {
  title: string;
  contents: string[];
};

export type FAQ = {
  question: string;
  answer: string;
};

export type ExploreSpace = {
  id: number | string;
  images: string[];
  spaceType: string;
  houseType: string;
  location: string;
  aminities: string[];
  price: {
    rent: number;
    unit: string;
  };
  availableFrom: string;
};
