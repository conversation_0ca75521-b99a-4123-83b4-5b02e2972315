import { dynamicIconImports } from "lucide-react/dynamic";
import { IconType } from "react-icons";

export interface Navigation {
  label: string;
  href: string;
}

export interface IOffcanvasNavigation extends Navigation {
  icon: keyof typeof dynamicIconImports;
}

export interface ISocialLink extends Navigation {
  icon: IconType;
}

export interface IFooterLinks {
  title: string;
  links: Navigation[];
}
