import React from "react";
import ListingCard from "@/components/Cards/ListingCard/ListingCard";
import { ExploreSpace } from "@/typescript/types";

interface ExploreSpacesSectionProps {
  listings: ExploreSpace[];
}

const ExploreSpacesSection = ({ listings }: ExploreSpacesSectionProps) => {
  return (
    <section>
      <div className="container space-y-4">
        <div>Search Filter</div>
        {/* Listings Section */}
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12 lg:col-span-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              {listings.map((exploreSpace: ExploreSpace, idx: number) => (
                <ListingCard key={idx} listing={exploreSpace} />
              ))}
            </div>
          </div>
          <div className="col-span-12 lg:col-span-4">Locations Map</div>
        </div>
      </div>
    </section>
  );
};

export default ExploreSpacesSection;
