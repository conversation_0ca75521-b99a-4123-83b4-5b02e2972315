import { useState, useEffect, useMemo } from "react";
import { useForm, UseFormReturn, FieldValues } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  FormIntent,
  SpaceType,
  FormConfig,
  StepConfig,
  getFormConfig,
  getStepFields,
} from "./formConfigs";

export interface UseMultiStepFormProps {
  intent?: FormIntent;
  spaceType?: SpaceType;
  onSubmit?: (data: FieldValues) => void;
  defaultValues?: Record<string, unknown>;
}

export interface UseMultiStepFormReturn {
  // Form instance
  form: UseFormReturn<FieldValues>;

  // Step management
  currentStep: number;
  totalSteps: number;
  isFirstStep: boolean;
  isLastStep: boolean;

  // Navigation
  nextStep: () => Promise<void>;
  prevStep: () => void;
  goToStep: (step: number) => void;

  // Configuration
  config: FormConfig | null;
  currentStepConfig: StepConfig | null;

  // Validation
  validateCurrentStep: () => Promise<boolean>;

  // Form submission
  handleSubmit: () => void;

  // Progress
  progress: number;
}

const getDefaultValues = (config: FormConfig | null): Record<string, unknown> => {
  if (!config) {
    return {
      firstName: "",
      lastName: "",
      email: "",
      intent: "rent" as FormIntent,
      spaceType: "private-room" as SpaceType,
    };
  }

  const defaults: Record<string, unknown> = {
    firstName: "",
    lastName: "",
    email: "",
    intent: config.intent,
    spaceType: config.spaceType,
    profilePhoto: undefined,
  };

  // Add aboutYou defaults based on form type
  if (config.intent === "find") {
    defaults.aboutYou = {
      genderIdentity: "",
      sexualOrientation: "",
      selfDescription: "",
      age: undefined,
      smokeCigarettes: false,
      workFromHome: false,
      travel: "",
      cleanliness: "",
      fluentLanguages: [],
      describeMyself: "",
      zodiac: "",
    };

    if (config.spaceType === "entire-place") {
      defaults.aboutYou.smokeMarijuana = false;
    }
  } else {
    defaults.aboutYou = {
      genderIdentity: "",
      sexualOrientation: "",
      selfDescription: "",
    };
  }

  // Add specific defaults based on form configuration
  if (config.intent === "rent") {
    if (config.spaceType === "private-room") {
      defaults.home = {
        address: "",
        residenceType: "",
        size: undefined,
        bedrooms: undefined,
        bathrooms: undefined,
        ownerOccupied: false,
        numberOfPeople: undefined,
        amenities: [],
        parking: "",
        neighborhood: "",
        currentPets: "",
        allowedPets: "",
        cigaretteSmoking: false,
        marijuanaSmoking: false,
        overnightGuests: false,
        homeDescription: "",
      };
      defaults.room = {
        availabilityDate: "",
        availabilityDuration: "",
        minimumDuration: "",
        monthlyRent: undefined,
        depositAmount: undefined,
        leaseRequired: false,
        requiredReferences: "",
        utilitiesIncluded: false,
        furnished: false,
        bedroomSize: "",
        brightness: "",
        bathroom: "",
        roomFeatures: [],
      };
      defaults.roommatePreferences = {
        genderIdentity: "",
        sexualOrientation: "",
        ageRange: "",
        smokingHabits: "",
        idealRoommate: "",
      };
    } else {
      defaults.space = {
        address: "",
        residenceType: "",
        size: undefined,
        bedrooms: undefined,
        bathrooms: undefined,
        brightness: "",
        amenities: [],
        parking: "",
        neighborhood: "",
        allowedPets: "",
        cigaretteSmoking: false,
        marijuanaSmoking: false,
        homeDescription: "",
      };
      defaults.rentalBasics = {
        availabilityDate: "",
        availabilityDuration: "",
        minimumDuration: "",
        monthlyRent: undefined,
        depositAmount: undefined,
        leaseRequired: false,
        requiredReferences: "",
        utilitiesIncluded: false,
        furnished: false,
      };
      defaults.tenantPreferences = {
        idealTenant: "",
      };
    }
    defaults.spacePhotos = undefined;
  } else {
    if (config.spaceType === "private-room") {
      defaults.room = {
        preferredLocation: "",
        rentalStartDate: "",
        maxMonthlyBudget: undefined,
        rentalAgreement: false,
        furnished: false,
        bathroom: "",
        bedroomSize: "",
        pets: "",
        parkingRequired: false,
      };
      defaults.roommatePreferences = {
        preferredGenderIdentity: "",
        preferredSexualOrientation: "",
        preferredAgeRange: "",
        preferredSmokingHabits: "",
      };
    } else {
      defaults.rentalPreferences = {
        preferredLocation: "",
        rentalStartDate: "",
        rentalDuration: "",
        maxMonthlyBudget: undefined,
        rentalAgreement: false,
        furnished: false,
        bedrooms: undefined,
        bathrooms: undefined,
        pets: "",
        parkingRequired: false,
      };
    }
  }

  return defaults;
};

export const useMultiStepForm = ({
  intent = "rent",
  spaceType = "private-room",
  onSubmit,
  defaultValues: customDefaults,
}: UseMultiStepFormProps = {}): UseMultiStepFormReturn => {
  const [currentStep, setCurrentStep] = useState(0);

  // Get form configuration
  const config = useMemo(() => {
    try {
      return getFormConfig(intent, spaceType);
    } catch {
      return null;
    }
  }, [intent, spaceType]);

  // Generate default values
  const defaultValues = useMemo(() => {
    const generated = getDefaultValues(config);
    return customDefaults ? { ...generated, ...customDefaults } : generated;
  }, [config, customDefaults]);

  // Initialize form
  const form = useForm({
    resolver: config ? zodResolver(config.schema) : undefined,
    defaultValues,
    mode: "onChange",
  });

  const { trigger, handleSubmit: rhfHandleSubmit, watch } = form;

  // Watch for intent/spaceType changes to update configuration
  const watchedIntent = watch("intent");
  const watchedSpaceType = watch("spaceType");

  useEffect(() => {
    if (watchedIntent !== intent || watchedSpaceType !== spaceType) {
      // Reset to first step when configuration changes
      setCurrentStep(0);
    }
  }, [watchedIntent, watchedSpaceType, intent, spaceType]);

  // Computed values
  const totalSteps = config?.steps.length || 0;
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;
  const progress = totalSteps > 0 ? ((currentStep + 1) / totalSteps) * 100 : 0;
  const currentStepConfig = config?.steps[currentStep] || null;

  // Validate current step
  const validateCurrentStep = async (): Promise<boolean> => {
    if (!config) return false;

    const fieldsToValidate = getStepFields(config, currentStep);
    if (fieldsToValidate.length === 0) return true;

    return await trigger(fieldsToValidate as (keyof FieldValues)[]);
  };

  // Navigation functions
  const nextStep = async () => {
    const isValid = await validateCurrentStep();
    if (isValid && currentStep < totalSteps - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 0 && step < totalSteps) {
      setCurrentStep(step);
    }
  };

  // Form submission
  const handleSubmit = () => {
    rhfHandleSubmit((data: FieldValues) => {
      if (onSubmit) {
        onSubmit(data);
      } else {
        console.log("Form submitted:", data);
      }
    })();
  };

  return {
    form,
    currentStep,
    totalSteps,
    isFirstStep,
    isLastStep,
    nextStep,
    prevStep,
    goToStep,
    config,
    currentStepConfig,
    validateCurrentStep,
    handleSubmit,
    progress,
  };
};
