import React from "react";
import Link from "next/link";
import Logo from "../Logo";
import SubscribeForm from "@/components/Forms/SubscribeForm/SubscribeForm";
import { footerNavigations, socialLinks } from "@/data";
import { IFooterLinks, ISocialLink, Navigation } from "@/typescript/interfaces";
import { FaRegEnvelope } from "react-icons/fa";
import { GrLocation } from "react-icons/gr";
import Image from "next/image";

const Footer = (): React.JSX.Element => {
  const currentYear = new Date().getFullYear();
  return (
    <footer className="bg-secondary text-white">
      <div className="container space-y-4">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <Logo variant="light" className="w-40!" />
          <div className="flex items-center gap-4">
            <p className="text-xl">Follow us on</p>
            <ul className="flex items-center gap-5">
              {socialLinks.map((link: ISocialLink, idx: number) => (
                <li key={idx}>
                  <Link href={link.href}>
                    <link.icon size={24} />
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-7 border-y py-4">
          <div>
            <h3 className="text-lg font-semibold mb-4 md:mb-6 lg:mb-7">Subscribe</h3>
            <SubscribeForm />
          </div>
          <div>
            {footerNavigations.map((footerLink: IFooterLinks) => (
              <FooterItems footerLink={footerLink} key={footerLink.title} />
            ))}
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-base inline-flex items-center gap-2"
                >
                  <FaRegEnvelope size={20} /> <EMAIL>
                </Link>
              </li>
              <li className="flex gap-2">
                <GrLocation size={20} />
                <address>
                  99 Fifth avense, 3rd Floor,
                  <br /> New York, NY 10001
                </address>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Download Our App</h3>
            <div className="flex flex-col gap-4">
              <Link href="#" className="w-36 block">
                <Image
                  src="/images/stores/apple-store.webp"
                  alt="Play Store"
                  width={350}
                  height={40}
                  className="w-full h-auto"
                />
              </Link>
              <Link href="#" className="w-36 block">
                <Image
                  src="/images/stores/play-store.webp"
                  alt="App Store"
                  width={350}
                  height={40}
                  className="w-full h-auto"
                />
              </Link>
            </div>
          </div>
        </div>
        <div className="text-center">
          <p>&copy; Copyright {currentYear} gayroom8.com. All Rights Reserved.</p>
        </div>
      </div>
    </footer>
  );
};

const FooterItems = ({ footerLink }: { footerLink: IFooterLinks }) => {
  return (
    <React.Fragment>
      <h3 className="text-lg font-semibold mb-4 md:mb-6 lg:mb-7">{footerLink.title}</h3>
      <ul className="space-y-3">
        {footerLink.links.map((link: Navigation, idx: number) => (
          <li key={idx}>
            <Link href={link.href} className="text-sm md:text-base hover:underline">
              {link.label}
            </Link>
          </li>
        ))}
      </ul>
    </React.Fragment>
  );
};

export default Footer;
