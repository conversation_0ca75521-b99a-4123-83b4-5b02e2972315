"use client";

import React, { useState } from "react";
import { MultiStepForm } from "./MultiStepForm";
import { FormIntent, SpaceType } from "./formConfigs";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const MultiStepFormDemo: React.FC = () => {
  const [intent, setIntent] = useState<FormIntent>("rent");
  const [spaceType, setSpaceType] = useState<SpaceType>("private-room");
  const [showForm, setShowForm] = useState(false);

  const handleSubmit = (data: Record<string, unknown>) => {
    console.log("Form submitted with data:", data);
    alert("Form submitted successfully! Check console for data.");
  };

  const resetForm = () => {
    setShowForm(false);
    setTimeout(() => setShowForm(true), 100);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Multi-Step Form Demo</h1>
          <p className="text-gray-600 mb-6">
            Test the reusable multi-step form with different configurations
          </p>

          {/* Configuration Controls */}
          <div className="bg-white p-6 rounded-lg shadow-md mb-8">
            <h2 className="text-lg font-semibold mb-4">Form Configuration</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Intent</label>
                <Select value={intent} onValueChange={(value) => setIntent(value as FormIntent)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rent">Rent</SelectItem>
                    <SelectItem value="find">Find</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Space Type</label>
                <Select
                  value={spaceType}
                  onValueChange={(value) => setSpaceType(value as SpaceType)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="private-room">Private Room</SelectItem>
                    <SelectItem value="entire-place">Entire Place</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2">
                <Button onClick={() => setShowForm(true)} disabled={showForm}>
                  Load Form
                </Button>
                <Button onClick={resetForm} variant="outline" disabled={!showForm}>
                  Reset
                </Button>
              </div>
            </div>

            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <p className="text-sm text-gray-600">
                <strong>Current Configuration:</strong> {intent} + {spaceType}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                This will load the appropriate form steps and validation schema
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        {showForm && (
          <MultiStepForm
            intent={intent}
            spaceType={spaceType}
            onSubmit={handleSubmit}
            title={`${intent === "rent" ? "List Your" : "Find"} ${spaceType === "private-room" ? "Private Room" : "Entire Place"}`}
            showProgress={true}
            showStepInfo={true}
          />
        )}

        {/* Instructions */}
        {!showForm && (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-lg font-semibold mb-4">Instructions</h2>
            <div className="space-y-3 text-sm text-gray-600">
              <p>
                1. Select an intent (Rent or Find) and space type (Private Room or Entire Place)
              </p>
              <p>
                2. Click &quot;Load Form&quot; to initialize the multi-step form with the selected
                configuration
              </p>
              <p>3. Navigate through the steps using the Next/Previous buttons</p>
              <p>4. Each step has its own validation rules based on the configuration</p>
              <p>5. Submit the form to see the collected data in the console</p>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-md">
              <h3 className="font-medium text-blue-900 mb-2">Available Configurations:</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>
                  • <strong>Rent + Private Room:</strong> Basic Info → About You → Home Details →
                  Room Details → Roommate Preferences
                </li>
                <li>
                  • <strong>Rent + Entire Place:</strong> Basic Info → About You → Space Details →
                  Rental Basics → Tenant Preferences
                </li>
                <li>
                  • <strong>Find + Private Room:</strong> Basic Info → Extended About You → Room
                  Preferences → Roommate Preferences
                </li>
                <li>
                  • <strong>Find + Entire Place:</strong> Basic Info → Extended About You → Rental
                  Preferences
                </li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MultiStepFormDemo;
