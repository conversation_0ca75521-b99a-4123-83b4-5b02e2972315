import React from "react";
import Link from "next/link";
import ListingImagesSlide from "./ListingImagesSlide";
import AddToWishlist from "@/components/Buttons/AddToWishlist/AddToWishlist";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { ExploreSpace } from "@/typescript/types";
import { Badge } from "@/components/ui/badge";

interface ListingCardProps {
  listing: ExploreSpace;
}

const ListingCard = ({ listing }: ListingCardProps) => {
  const { id, images, spaceType, houseType, location, aminities, price, availableFrom } = listing;

  return (
    <Card className="text-gray-700 py-3 relative">
      <CardHeader className="px-3">
        <ListingImagesSlide images={images} />
        {/* Add to Wishlist */}
        <AddToWishlist className="absolute top-5 right-5 z-10 rounded-full cursor-pointer" />
      </CardHeader>
      <CardContent className="px-3 space-y-4">
        <CardTitle className="flex items-center gap-2">
          <DynamicIcon name="map-pin" size={20} color="#2980B9" />
          {location}
        </CardTitle>
        <ul className="flex flex-wrap items-center gap-3">
          {aminities.map((amenity: string, idx: number) => (
            <li key={idx} className="text-sm">
              • {amenity}
            </li>
          ))}
        </ul>
        <div className="flex items-center gap-2">
          <Badge className="px-4 py-2 bg-secondary/10 text-secondary">{spaceType}</Badge>
          <Badge className="px-4 py-2 bg-primary/10 text-secondary">{houseType}</Badge>
          <Badge className="px-4 py-2 bg-secondary/10 text-secondary">{availableFrom}</Badge>
        </div>
      </CardContent>
      <CardFooter className="px-3 justify-between">
        <Link href={`/explore-spaces/${id}`} className="flex items-center gap-2 hover:underline">
          View More <DynamicIcon name="move-right" size={16} />
        </Link>
        <p>
          <span className="font-bold text-lg text-primary">${price.rent}</span>/{price.unit}
        </p>
      </CardFooter>
    </Card>
  );
};

export default ListingCard;
