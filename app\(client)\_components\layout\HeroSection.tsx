"use client";

import React from "react";
import Link from "next/link";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import { heroContents } from "@/data";
import { Button } from "@/components/ui/button";
import { THeroContent } from "@/typescript/types";
import "swiper/css/pagination";
import QuickSearchBar from "@/components/QuickSearchBar/QuickSearchBar";

const HeroSection = () => {
  return (
    <section className={"w-full h-[800px] py-0 relative"}>
      <Swiper
        loop={true}
        pagination={{
          dynamicBullets: true,
        }}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        modules={[Pagination, Autoplay]}
      >
        {heroContents.map((content: THeroContent, idx: number) => (
          <SwiperSlide
            key={idx}
            className="h-full! relative"
            style={{
              backgroundImage: `url(${content.image})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
          >
            <div className="absolute inset-0 bg-black/40" />
            <div className="container z-10 relative">
              <div className="flex flex-col gap-4 h-full justify-center mt-6 md:mt-12 w-full sm:w-3/5 md:w-2/3 lg:w-1/3 xl:w-2/6">
                <h2 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-white leading-snug">
                  {content.title}
                </h2>
                <Button asChild className="hidden md:block rounded-full text-center">
                  <Link href={"#"}>{content.description}</Link>
                </Button>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
      <QuickSearchBar className="absolute left-0 right-0 bottom-60 z-20 max-w-sm md:max-w-2xl lg:max-w-4xl xl:max-w-5xl mx-auto" />
    </section>
  );
};

export default HeroSection;
