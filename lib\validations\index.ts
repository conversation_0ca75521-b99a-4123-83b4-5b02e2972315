import { z } from "zod";

export const subscriptionFormSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export const quickSearchBarSchema = z.object({
  search: z.string().min(1, "Search term is required"),
});

export const contactUsFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required"),
  subject: z.string().min(1, "Subject is required"),
  message: z.string().min(30, "Message is required, minimum 30 characters"),
});

export const loginFormSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password is required, minimum 8 characters"),
  rememberMe: z.boolean().optional(),
});

// User Schema
export const userSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  profilePhoto: z.instanceof(File).optional(),
  aboutYou: z.object({
    genderIdentity: z.string().min(1, "Gender identity is required"),
    sexualOrientation: z.string().min(1, "Sexual orientation is required"),
    selfDescription: z.string().min(10, "Description must be at least 10 characters"),
  }),
});

// Property Common Schema
export const propertyCommonSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  intent: z.enum(["rent", "find"]),
  spaceType: z.enum(["private-room", "entire-place"]),
  profilePhoto: z.instanceof(File).optional(),
});

// Private Room Schema
export const rentPrivateRoomSchema = propertyCommonSchema.extend({
  home: z.object({
    address: z.string().min(1, "Address is required"),
    residenceType: z.string().min(1, "Residence type is required"),
    size: z.number().min(1, "Size must be positive"),
    bedrooms: z.number().min(1, "Number of bedrooms is required"),
    bathrooms: z.number().min(1, "Number of bathrooms is required"),
    ownerOccupied: z.boolean(),
    numberOfPeople: z.number().min(1, "Number of people is required"),
    amenities: z.array(z.string()).min(1, "At least one amenity is required"),
    parking: z.string().min(1, "Parking info is required"),
    neighborhood: z.string().min(1, "Neighborhood is required"),
    currentPets: z.string().optional(),
    allowedPets: z.string().min(1, "Allowed pets is required"),
    cigaretteSmoking: z.boolean(),
    marijuanaSmoking: z.boolean(),
    overnightGuests: z.boolean(),
    homeDescription: z.string().min(10, "Home description must be at least 10 characters"),
  }),
  room: z.object({
    availabilityDate: z.string().min(1, "Availability date is required"),
    availabilityDuration: z.string().min(1, "Availability duration is required"),
    minimumDuration: z.string().min(1, "Minimum duration is required"),
    monthlyRent: z.number().min(1, "Monthly rent is required"),
    depositAmount: z.number().min(0, "Deposit amount is required"),
    leaseRequired: z.boolean(),
    requiredReferences: z.string().min(1, "Required references is required"),
    utilitiesIncluded: z.boolean(),
    furnished: z.boolean(),
    bedroomSize: z.string().min(1, "Bedroom size is required"),
    brightness: z.string().min(1, "Brightness is required"),
    bathroom: z.string().min(1, "Bathroom info is required"),
    roomFeatures: z.array(z.string()).min(1, "At least one room feature is required"),
  }),
  spacePhotos: z.instanceof(File).optional(),
  roommatePreferences: z.object({
    genderIdentity: z.string().min(1, "Preferred gender identity is required"),
    sexualOrientation: z.string().min(1, "Preferred sexual orientation is required"),
    ageRange: z.string().min(1, "Preferred age range is required"),
    smokingHabits: z.string().min(1, "Preferred smoking habits is required"),
    idealRoommate: z.string().min(10, "Ideal roommate description must be at least 10 characters"),
  }),
});

// Entire Place Schema
export const rentEntirePlaceSchema = propertyCommonSchema.extend({
  space: z.object({
    address: z.string().min(1, "Address is required"),
    residenceType: z.string().min(1, "Residence type is required"),
    size: z.number().min(1, "Size must be positive"),
    bedrooms: z.number().min(1, "Number of bedrooms is required"),
    bathrooms: z.number().min(1, "Number of bathrooms is required"),
    brightness: z.string().min(1, "Brightness is required"),
    amenities: z.array(z.string()).min(1, "At least one amenity is required"),
    parking: z.string().min(1, "Parking info is required"),
    neighborhood: z.string().min(1, "Neighborhood is required"),
    allowedPets: z.string().min(1, "Allowed pets is required"),
    cigaretteSmoking: z.boolean(),
    marijuanaSmoking: z.boolean(),
    homeDescription: z.string().min(10, "Home description must be at least 10 characters"),
  }),
  spacePhotos: z.instanceof(File).optional(),
  rentalBasics: z.object({
    availabilityDate: z.string().min(1, "Availability date is required"),
    availabilityDuration: z.string().min(1, "Availability duration is required"),
    minimumDuration: z.string().min(1, "Minimum duration is required"),
    monthlyRent: z.number().min(1, "Monthly rent is required"),
    depositAmount: z.number().min(0, "Deposit amount is required"),
    leaseRequired: z.boolean(),
    requiredReferences: z.string().min(1, "Required references is required"),
    utilitiesIncluded: z.boolean(),
    furnished: z.boolean(),
  }),
  tenantPreferences: z.object({
    idealTenant: z.string().min(10, "Ideal tenant description must be at least 10 characters"),
  }),
});

// Find Private Room Schema
export const findPrivateRoomSchema = propertyCommonSchema.extend({
  aboutYou: userSchema.shape.aboutYou.extend({
    age: z.number().min(18, "Age must be at least 18"),
    smokeCigarettes: z.boolean(),
    workFromHome: z.boolean(),
    travel: z.string().min(1, "Travel preference is required"),
    cleanliness: z.string().min(1, "Cleanliness preference is required"),
    fluentLanguages: z.array(z.string()).min(1, "At least one fluent language is required"),
    describeMyself: z.string().min(1, "Description of yourself is required"),
    zodiac: z.string().optional(),
  }),
  room: z.object({
    preferredLocation: z.string().min(1, "Preferred location is required"),
    rentalStartDate: z.string().min(1, "Rental start date is required"),
    maxMonthlyBudget: z.number().min(1, "Maximum monthly budget is required"),
    rentalAgreement: z.boolean(),
    furnished: z.boolean(),
    bathroom: z.string().min(1, "Bathroom preference is required"),
    bedroomSize: z.string().min(1, "Bedroom size preference is required"),
    pets: z.string().min(1, "Pet preference is required"),
    parkingRequired: z.boolean(),
  }),
  roommatePreferences: z.object({
    preferredGenderIdentity: z.string().min(1, "Preferred gender identity is required"),
    preferredSexualOrientation: z.string().min(1, "Preferred sexual orientation is required"),
    preferredAgeRange: z.string().min(1, "Preferred age range is required"),
    preferredSmokingHabits: z.string().min(1, "Preferred smoking habits is required"),
  }),
});

// Find Entire Place Schema
export const findEntirePlaceSchema = propertyCommonSchema.extend({
  aboutYou: userSchema.shape.aboutYou.extend({
    age: z.number().min(18, "Age must be at least 18"),
    smokeCigarettes: z.boolean(),
    smokeMarijuana: z.boolean(),
    workFromHome: z.boolean(),
    travel: z.string().min(1, "Travel preference is required"),
    cleanliness: z.string().min(1, "Cleanliness preference is required"),
    fluentLanguages: z.array(z.string()).min(1, "At least one fluent language is required"),
    describeMyself: z.string().min(1, "Description of yourself is required"),
    zodiac: z.string().optional(),
  }),
  rentalPreferences: z.object({
    preferredLocation: z.string().min(1, "Preferred location is required"),
    rentalStartDate: z.string().min(1, "Rental start date is required"),
    rentalDuration: z.string().min(1, "Rental duration is required"),
    maxMonthlyBudget: z.number().min(1, "Maximum monthly budget is required"),
    rentalAgreement: z.boolean(),
    furnished: z.boolean(),
    bedrooms: z.number().min(1, "Number of bedrooms is required"),
    bathrooms: z.number().min(1, "Number of bathrooms is required"),
    pets: z.string().min(1, "Pet preference is required"),
    parkingRequired: z.boolean(),
  }),
});

export type SubscriptionFormValues = z.infer<typeof subscriptionFormSchema>;
export type QuickSearchBarValues = z.infer<typeof quickSearchBarSchema>;
export type ContactUsFormValues = z.infer<typeof contactUsFormSchema>;
export type LoginFormValues = z.infer<typeof loginFormSchema>;
export type UserValues = z.infer<typeof userSchema>;
export type PropertyCommonValues = z.infer<typeof propertyCommonSchema>;
export type RentPrivateRoomValues = z.infer<typeof rentPrivateRoomSchema>;
export type RentEntirePlaceValues = z.infer<typeof rentEntirePlaceSchema>;
export type FindPrivateRoomValues = z.infer<typeof findPrivateRoomSchema>;
export type FindEntirePlaceValues = z.infer<typeof findEntirePlaceSchema>;
