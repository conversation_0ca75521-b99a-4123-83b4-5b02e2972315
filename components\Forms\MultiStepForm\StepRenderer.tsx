import React from "react";
import { UseFormReturn, FieldValues } from "react-hook-form";
import { FormIntent, SpaceType, StepConfig } from "./formConfigs";

// Import step components
import BasicInfoStep from "./steps/BasicInfoStep";
import AboutYouStep from "./steps/AboutYouStep";
import ExtendedAboutYouStep from "./steps/ExtendedAboutYouStep";
import ProfilePhotoStep from "./steps/ProfilePhotoStep";
import HomeDetailsStep from "./steps/HomeDetailsStep";
import SpaceDetailsStep from "./steps/SpaceDetailsStep";
import RoomDetailsStep from "./steps/RoomDetailsStep";
import RoomPreferencesStep from "./steps/RoomPreferencesStep";
import PhotoUploadStep from "./steps/PhotoUploadStep";
import RoommatePreferencesStep from "./steps/RoommatePreferencesStep";
import FindRoommatePreferencesStep from "./steps/FindRoommatePreferencesStep";
import RentalBasicsStep from "./steps/RentalBasicsStep";
import TenantPreferencesStep from "./steps/TenantPreferencesStep";
import RentalPreferencesStep from "./steps/RentalPreferencesStep";

export interface StepRendererProps {
  form: UseFormReturn<FieldValues>;
  stepConfig: StepConfig;
  intent: FormIntent;
  spaceType: SpaceType;
}

const stepComponents = {
  BasicInfoStep,
  AboutYouStep,
  ExtendedAboutYouStep,
  ProfilePhotoStep,
  HomeDetailsStep,
  SpaceDetailsStep,
  RoomDetailsStep,
  RoomPreferencesStep,
  PhotoUploadStep,
  RoommatePreferencesStep,
  FindRoommatePreferencesStep,
  RentalBasicsStep,
  TenantPreferencesStep,
  RentalPreferencesStep,
} as const;

export const StepRenderer: React.FC<StepRendererProps> = ({
  form,
  stepConfig,
  intent,
  spaceType,
}) => {
  const componentName = stepConfig.component as keyof typeof stepComponents;
  const StepComponent = stepComponents[componentName];

  if (!StepComponent) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Step component &quot;{componentName}&quot; not found</p>
      </div>
    );
  }

  // Handle combined steps (e.g., aboutYou + profilePhoto)
  if (stepConfig.fields.includes("aboutYou") && stepConfig.fields.includes("profilePhoto")) {
    return (
      <div className="space-y-8">
        <AboutYouStep form={form} intent={intent} spaceType={spaceType} />
        <ProfilePhotoStep form={form} />
      </div>
    );
  }

  if (stepConfig.fields.includes("room") && stepConfig.fields.includes("spacePhotos")) {
    return (
      <div className="space-y-8">
        <RoomDetailsStep form={form} isFind={false} />
        <PhotoUploadStep form={form} />
      </div>
    );
  }

  if (stepConfig.fields.includes("space") && stepConfig.fields.includes("spacePhotos")) {
    return (
      <div className="space-y-8">
        <SpaceDetailsStep form={form} />
        <PhotoUploadStep form={form} />
      </div>
    );
  }

  // Render single step component
  const commonProps = {
    form,
    intent,
    spaceType,
  };

  switch (componentName) {
    case "BasicInfoStep":
      return <BasicInfoStep {...commonProps} />;
    case "AboutYouStep":
      return <AboutYouStep {...commonProps} />;
    case "ExtendedAboutYouStep":
      return <ExtendedAboutYouStep {...commonProps} />;
    case "ProfilePhotoStep":
      return <ProfilePhotoStep {...commonProps} />;
    case "HomeDetailsStep":
      return <HomeDetailsStep {...commonProps} />;
    case "SpaceDetailsStep":
      return <SpaceDetailsStep {...commonProps} />;
    case "RoomDetailsStep":
      return <RoomDetailsStep {...commonProps} isFind={false} />;
    case "RoomPreferencesStep":
      return <RoomPreferencesStep {...commonProps} />;
    case "PhotoUploadStep":
      return <PhotoUploadStep {...commonProps} />;
    case "RoommatePreferencesStep":
      return <RoommatePreferencesStep {...commonProps} />;
    case "FindRoommatePreferencesStep":
      return <FindRoommatePreferencesStep {...commonProps} />;
    case "RentalBasicsStep":
      return <RentalBasicsStep {...commonProps} />;
    case "TenantPreferencesStep":
      return <TenantPreferencesStep {...commonProps} />;
    case "RentalPreferencesStep":
      return <RentalPreferencesStep {...commonProps} />;
    default:
      return (
        <div className="text-center py-8">
          <p className="text-red-500">Unknown step component: {componentName}</p>
        </div>
      );
  }
};
