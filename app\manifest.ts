import type { MetadataRoute } from "next";

export default function manifest(): MetadataRoute.Manifest {
  return {
    id: "/",
    name: "<PERSON>room8",
    short_name: "Gayroom8",
    description: "Gayroom8 is a rental spaces platform for LGBTQ+ people.",
    start_url: "/",
    display: "standalone",
    background_color: "#8e44ad",
    theme_color: "#2980b9",
    display_override: ["fullscreen", "standalone", "minimal-ui"],
    icons: [
      {
        src: "/favicon.ico",
        sizes: "any",
        type: "image/x-icon",
      },
      {
        src: "/images/icons/android-icon-36x36.png",
        sizes: "36x36",
        type: "image/png",
      },
      {
        src: "/images/icons/android-icon-48x48.png",
        sizes: "48x48",
        type: "image/png",
      },
      {
        src: "/images/icons/android-icon-72x72.png",
        sizes: "72x72",
        type: "image/png",
      },
      {
        src: "/images/icons/android-icon-96x96.png",
        sizes: "96x96",
        type: "image/png",
      },
      {
        src: "/images/icons/android-icon-144x144.png",
        sizes: "144x144",
        type: "image/png",
      },
      {
        src: "/images/icons/android-icon-192x192.png",
        sizes: "192x192",
        type: "image/png",
      },
      {
        src: "/images/icons/apple-icon-57x57.png",
        sizes: "57x57",
        type: "image/png",
      },
      {
        src: "/images/icons/apple-icon-60x60.png",
        sizes: "60x60",
        type: "image/png",
      },
      {
        src: "/images/icons/apple-icon-72x72.png",
        sizes: "72x72",
        type: "image/png",
      },
      {
        src: "/images/icons/apple-icon-76x76.png",
        sizes: "76x76",
        type: "image/png",
      },
      {
        src: "/images/icons/apple-icon-114x114.png",
        sizes: "114x114",
        type: "image/png",
      },
      {
        src: "/images/icons/apple-icon-120x120.png",
        sizes: "120x120",
        type: "image/png",
      },
      {
        src: "/images/icons/apple-icon-144x144.png",
        sizes: "144x144",
        type: "image/png",
      },
      {
        src: "/images/icons/apple-icon-152x152.png",
        sizes: "152x152",
        type: "image/png",
      },
      {
        src: "/images/icons/apple-icon-180x180.png",
        sizes: "180x180",
        type: "image/png",
      },
    ],
    screenshots: [
      {
        src: "/images/screenshots/home-screen.png",
        sizes: "1907x867",
        type: "image/png",
        label: "Home Screen",
        form_factor: "wide",
      },
      {
        src: "/images/screenshots/home-mobile-screen.png",
        sizes: "426x823",
        type: "image/png",
        label: "Home Screen",
        form_factor: "narrow",
      },
    ],
  };
}
