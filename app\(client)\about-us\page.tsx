import React from "react";
import Image from "next/image";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import { FaArrowCircleRight } from "react-icons/fa";

const AboutUsPage = () => {
  return (
    <>
      <TopBanner title="About Us" />
      <section>
        <div className="container">
          <div className="grid grid-cols-12 gap-6 items-center">
            <div className="col-span-12 lg:col-span-4">
              <Image
                src={"/images/about/about.webp"}
                alt="About Us"
                width={1000}
                height={1000}
                priority
              />
            </div>
            <div className="col-span-12 lg:col-span-8 space-y-5">
              <p className="text-lg text-primary font-medium flex gap-4 items-center tracking-widest">
                <FaArrowCircleRight size={20} /> Founder & CEO of Gayroom8
              </p>
              <h2 className="text-3xl lg:text-4xl font-semibold text-secondary"><PERSON></h2>
              <div className="text-lg text-gray-700 leading-relaxed">
                <p>
                  Twenty years from now you will be more disappointed by the things you didn’t do
                  than by the things you did do. So throw off the bowlines, sail away from the safe
                  harbor. Catch the trade winds in your sails. Explore. Dream. Discover.
                </p>
                <p>–H. Jackson Brown, Jr.</p>
              </div>
            </div>
          </div>
          <div className="space-y-5 mt-5 text-lg text-gray-700 leading-relaxed">
            <p>
              It was 2008 and for the very first time, American citizens were allowed to apply for a
              ‘work-holiday visa’ to live and work in Australia. Being the gypsy that I am, I
              couldn’t resist. After getting my visa approval I packed two suitcases and boarded a
              one-way flight to Sydney.
            </p>
            <p>
              I knew absolutely no one on the other end. My only lead was a Mormon family I was
              referred to by an old missionary friend. I landed at Sydney’s Kingsford Smith Airport
              and that gracious family picked me up and we drove back to their suburban home… ninety
              minutes from Sydney. The basement room, in the suburbs, wasn’t quite the experience I
              had envisioned. Despair set in. I thought to myself,{" "}
              <i>
                ‘How am I ever going to find a good place to live with someone like me? Surely there
                must be a website or something for gay people to find roommates.’
              </i>{" "}
              There wasn’t.
            </p>
            <p>
              After reaching out to friends of friends with no leads, eventually I met a guy on
              Oxford Street who introduced me to a guy, who offered me a temporary place in the city
              while I looked for something more permanent. Eventually I found a place, but two years
              later I relocated to New York and again, the hunt to find a roommate was on. Again, I
              looked for a quality website to find a gay roommate. Nothing. A few years later I
              moved to Los Angeles… nothing. Then Madrid, then Barcelona, then Miami… still nothing.
              You get the picture.
            </p>
            <p>
              So I decided I was done waiting for ‘someone’ to build a website. I was going to build
              my own. I wanted to provide a safe, quality resource for the LGBTQ+ community to
              easily find roommates all over the world. I wanted to provide a way to easily connect
              with others to live, or spend time, in places they’ve always wanted to see and
              experience. I wanted to connect gay faces with gay spaces, and that’s why I created
              gayoom8.com <br />
              <br /> It’s a small gay world.
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default AboutUsPage;
