import React from "react";
import Link from "next/link";
import Image from "next/image";

type LogoProps = {
  path?: string;
  className?: string;
  variant?: "light" | "dark";
};

const Logo = ({ path = "/", className = "", variant = "dark" }: LogoProps) => {
  if (variant === "dark") {
    return (
      <Link href={path}>
        <Image
          src={`/images/logos/logo-dark.png`}
          alt="Gayroom8 Logo"
          width={150}
          height={150}
          priority
          className={`w-auto h-auto ${className}`}
        />
      </Link>
    );
  }

  return (
    <Link href={path}>
      <Image
        src={`/images/logos/logo-white.png`}
        alt="Gayroom8 Logo"
        width={150}
        height={150}
        priority
        className={`w-auto h-auto ${className}`}
      />
    </Link>
  );
};

export default Logo;
